#%% md
1) 数据读取与训练/测试集划分
数据格式：每行 title$$$like（like 为 0/1）。你给了两条路径（正/负样本）。为了兼容两种来源，下面代码会：

若行内已有 $$$，按其标签读取；

否则：来自正样本文件的行标注为 1，负样本文件的行标注为 0。
#%%
# train_test_split.py
from __future__ import annotations
import os, random, csv
from pathlib import Path
import pandas as pd
from sklearn.model_selection import train_test_split

POS_PATH = "/Users/<USER>/workplace/rssReco/ttrss_like.csv"
NEG_PATH = "/Users/<USER>/workplace/rssReco/ttrss_unlike.csv"
OUT_DIR = "/Users/<USER>/workplace/rssReco/out"
SEED = 20250727

os.makedirs(OUT_DIR, exist_ok=True)
random.seed(SEED)

def load_file(path: str, default_label: int|None) -> list[tuple[str,int]]:
    rows = []
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            if "$$$" in line:
                title, lbl = line.rsplit("$$$", 1)
                try:
                    y = int(lbl)
                except:
                    continue
                rows.append((title.strip(), y))
            else:
                if default_label is None:
                    continue
                rows.append((line, default_label))
    return rows

data = []
data += load_file(POS_PATH, default_label=1)
data += load_file(NEG_PATH, default_label=0)

df = pd.DataFrame(data, columns=["title","label"]).dropna()
df = df.drop_duplicates(subset=["title"])
print("Total samples:", len(df), "pos:", df["label"].sum(), "neg:", (1-df["label"]).sum())

# 按标签分层划分（可调 test_size）
train_df, test_df = train_test_split(
    df, test_size=0.2, random_state=SEED, stratify=df["label"]
)

train_df.to_csv(Path(OUT_DIR, "train.csv"), index=False)
test_df.to_csv(Path(OUT_DIR, "test.csv"), index=False)
print("Saved:", str(Path(OUT_DIR, "train.csv")), str(Path(OUT_DIR, "test.csv")))

#%% md
2) 特征工程与模型训练（字符 n‑gram + TF‑IDF + 逻辑回归）
为避免中文分词在 Java 侧复现的复杂度，采用字符 n‑gram（建议 2–5gram）+ TF‑IDF。这在短标题分类里表现稳定，且 Java 推理易实现。
#%%

#%%
# train_lr_model.py
from __future__ import annotations
import json, os
from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    accuracy_score, roc_auc_score, classification_report, confusion_matrix
)

OUT_DIR = "/Users/<USER>/workplace/rssReco/out"
SEED = 20250727

train_df = pd.read_csv(Path(OUT_DIR, "train.csv"))
test_df  = pd.read_csv(Path(OUT_DIR, "test.csv"))

# 字符 n-gram；不开启子线性TF，使用标准IDF与L2归一化，便于 Java 侧复现
vectorizer = TfidfVectorizer(
    analyzer="char",
    ngram_range=(2, 5),
    min_df=2,             # 过滤极少出现的n-gram，可按数据量调整
    max_features=200000,  # 上限以免维度过大
    norm="l2",
    use_idf=True,
    smooth_idf=True,
    sublinear_tf=False,
)

X_train = vectorizer.fit_transform(train_df["title"].astype(str))
y_train = train_df["label"].values
X_test  = vectorizer.transform(test_df["title"].astype(str))
y_test  = test_df["label"].values

# 若类别不均衡，class_weight='balanced' 可缓解
clf = LogisticRegression(
    solver="liblinear",       # 稳定适用于稀疏特征
    max_iter=1000,
    class_weight="balanced",
    random_state=SEED
)
clf.fit(X_train, y_train)

# 评估
proba = clf.predict_proba(X_test)[:, 1]
pred  = (proba >= 0.5).astype(int)
print("Accuracy:", accuracy_score(y_test, pred))
try:
    print("ROC-AUC:", roc_auc_score(y_test, proba))
except ValueError:
    print("ROC-AUC: (需要正负样本同时存在)")
print(confusion_matrix(y_test, pred))
print(classification_report(y_test, pred, digits=4))

# —— 导出推理所需参数到 JSON（Java 侧直接复现） ——
vocab = vectorizer.vocabulary_         # {ngram: col_index}
idf   = vectorizer.idf_.tolist()       # 与列索引对齐
coef  = clf.coef_.ravel().tolist()     # 与列索引对齐的一维权重
inter = float(clf.intercept_[0])

export = {
    "vectorizer": {
        "type": "tfidf_char",
        "ngram_min": 2,
        "ngram_max": 5,
        "min_df": 2,
        "norm": "l2",
        "use_idf": True,
        "smooth_idf": True,
        "sublinear_tf": False,
        "vocabulary": vocab,   # 注意：key=ngram, value=列索引
        "idf": idf             # idf[i] 对应列 i
    },
    "model": {
        "type": "logreg_binary",
        "coef": coef,          # coef[i] 对应列 i
        "intercept": inter
    },
    "meta": {
        "train_size": int(X_train.shape[0]),
        "test_size": int(X_test.shape[0]),
        "seed": SEED,
        "feature_dim": int(X_train.shape[1]),
        "metrics": {
            "accuracy": float(accuracy_score(y_test, pred)),
            "roc_auc": float(roc_auc_score(y_test, proba)) if len(set(y_test))==2 else None
        }
    }
}

os.makedirs(OUT_DIR, exist_ok=True)
with open(Path(OUT_DIR, "rss_lr_model.json"), "w", encoding="utf-8") as f:
    json.dump(export, f, ensure_ascii=False)

print("Saved model to:", str(Path(OUT_DIR, "rss_lr_model.json")))

#%% md

#%%
