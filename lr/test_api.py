#!/usr/bin/env python3
"""
RSS分类器API测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_model_info():
    """测试模型信息API"""
    print("🔍 测试模型信息API...")
    try:
        response = requests.get(f"{BASE_URL}/api/model/info")
        if response.status_code == 200:
            info = response.json()
            print("✅ 模型信息获取成功:")
            print(f"  - 特征维度: {info['featureDim']}")
            print(f"  - 训练样本: {info['trainSize']}")
            print(f"  - 测试样本: {info['testSize']}")
            print(f"  - 准确率: {info['formattedAccuracy']}")
            print(f"  - ROC-AUC: {info['formattedRocAuc']}")
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_single_classification():
    """测试单个标题分类"""
    print("\n📝 测试单个标题分类...")
    
    test_titles = [
        "人工智能技术的最新发展趋势",
        "如何提高工作效率的10个技巧", 
        "深度学习在图像识别中的应用",
        "Spring Boot开发实战指南",
        "机器学习算法详解与实践"
    ]
    
    for title in test_titles:
        try:
            response = requests.post(f"{BASE_URL}/api/classify/single", 
                                   params={"title": title})
            if response.status_code == 200:
                result = response.json()
                print(f"  📄 {title}")
                print(f"     结果: {result['label']} ({result['formattedProbability']})")
            else:
                print(f"❌ 分类失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")

def test_batch_classification():
    """测试批量标题分类"""
    print("\n📚 测试批量标题分类...")
    
    batch_titles = [
        "Python数据分析入门教程",
        "区块链技术原理与应用", 
        "云计算服务架构设计",
        "前端开发最佳实践",
        "数据库优化技巧分享"
    ]
    
    try:
        response = requests.post(f"{BASE_URL}/api/classify/batch",
                               json=batch_titles,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            results = response.json()
            print("✅ 批量分类成功:")
            
            like_count = sum(1 for r in results if r['prediction'] == 1)
            dislike_count = len(results) - like_count
            
            print(f"  📊 统计: {len(results)}个标题, {like_count}个喜欢, {dislike_count}个不喜欢")
            print("  📋 详细结果:")
            
            for i, result in enumerate(results, 1):
                print(f"    {i}. {result['title']}")
                print(f"       {result['label']} ({result['formattedProbability']})")
        else:
            print(f"❌ 批量分类失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保应用已启动并运行在 http://localhost:8080")
        return False

def main():
    """主测试函数"""
    print("🧪 RSS分类器API测试")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        print("\n💡 启动应用的方法:")
        print("  mvn spring-boot:run")
        print("  或")
        print("  java -jar target/rss-classifier-1.0.0.jar")
        return
    
    # 等待服务器完全启动
    print("⏳ 等待服务器完全启动...")
    time.sleep(2)
    
    # 运行测试
    test_model_info()
    test_single_classification()
    test_batch_classification()
    
    print("\n🎉 测试完成!")
    print(f"🌐 Web界面: {BASE_URL}")

if __name__ == "__main__":
    main()
