#!/usr/bin/env python3
"""
词汇表构建和管理
"""

import json
import torch
from collections import Counter
from typing import List, Dict, Optional
from pathlib import Path

class Vocabulary:
    """词汇表类"""
    
    def __init__(self, min_freq: int = 2, max_size: Optional[int] = None):
        self.min_freq = min_freq
        self.max_size = max_size
        
        # 特殊标记
        self.PAD_TOKEN = '<PAD>'
        self.UNK_TOKEN = '<UNK>'
        self.START_TOKEN = '<START>'
        self.END_TOKEN = '<END>'
        
        # 词汇表
        self.word2idx = {}
        self.idx2word = {}
        self.word_counts = Counter()
        
        # 初始化特殊标记
        self._init_special_tokens()
    
    def _init_special_tokens(self):
        """初始化特殊标记"""
        special_tokens = [self.PAD_TOKEN, self.UNK_TOKEN, self.START_TOKEN, self.END_TOKEN]
        
        for i, token in enumerate(special_tokens):
            self.word2idx[token] = i
            self.idx2word[i] = token
    
    def build_from_texts(self, texts: List[str]):
        """从文本列表构建词汇表"""
        print("构建词汇表...")
        
        # 统计词频
        for text in texts:
            words = text.split()
            self.word_counts.update(words)
        
        print(f"总词汇数: {len(self.word_counts)}")
        
        # 按频率排序，过滤低频词
        sorted_words = sorted(self.word_counts.items(), key=lambda x: x[1], reverse=True)
        
        # 添加词汇到词汇表
        for word, count in sorted_words:
            if count >= self.min_freq:
                if self.max_size and len(self.word2idx) >= self.max_size:
                    break
                
                if word not in self.word2idx:
                    idx = len(self.word2idx)
                    self.word2idx[word] = idx
                    self.idx2word[idx] = word
        
        print(f"词汇表大小: {len(self.word2idx)} (min_freq={self.min_freq})")
        print(f"覆盖率: {self._calculate_coverage(texts):.2%}")
    
    def _calculate_coverage(self, texts: List[str]) -> float:
        """计算词汇表覆盖率"""
        total_words = 0
        covered_words = 0
        
        for text in texts:
            words = text.split()
            total_words += len(words)
            covered_words += sum(1 for word in words if word in self.word2idx)
        
        return covered_words / total_words if total_words > 0 else 0.0
    
    def encode_text(self, text: str, max_length: Optional[int] = None, 
                   add_special_tokens: bool = False) -> List[int]:
        """将文本编码为索引序列"""
        words = text.split()
        
        if add_special_tokens:
            words = [self.START_TOKEN] + words + [self.END_TOKEN]
        
        # 转换为索引
        indices = []
        for word in words:
            if word in self.word2idx:
                indices.append(self.word2idx[word])
            else:
                indices.append(self.word2idx[self.UNK_TOKEN])
        
        # 截断或填充
        if max_length:
            if len(indices) > max_length:
                indices = indices[:max_length]
            else:
                indices.extend([self.word2idx[self.PAD_TOKEN]] * (max_length - len(indices)))
        
        return indices
    
    def decode_indices(self, indices: List[int], remove_special_tokens: bool = True) -> str:
        """将索引序列解码为文本"""
        words = []
        
        for idx in indices:
            if idx in self.idx2word:
                word = self.idx2word[idx]
                
                if remove_special_tokens and word in [self.PAD_TOKEN, self.START_TOKEN, self.END_TOKEN]:
                    continue
                
                words.append(word)
        
        return ' '.join(words)
    
    def encode_dataset(self, dataset, max_length: int = 128):
        """编码整个数据集"""
        print(f"编码数据集 ({len(dataset)} 样本)...")
        
        # 创建新的数据集类
        class EncodedDataset:
            def __init__(self, original_dataset, vocab, max_length):
                self.original_dataset = original_dataset
                self.vocab = vocab
                self.max_length = max_length
            
            def __len__(self):
                return len(self.original_dataset)
            
            def __getitem__(self, idx):
                item = self.original_dataset[idx]
                
                # 编码文本
                if 'text' in item:
                    text = item['text']
                    encoded = self.vocab.encode_text(text, max_length=self.max_length)
                    
                    return {
                        'text': torch.tensor(encoded, dtype=torch.long),
                        'label': item['label']
                    }
                else:
                    # 如果是BERT数据集，直接返回
                    return item
        
        return EncodedDataset(dataset, self, max_length)
    
    def save(self, filepath: str):
        """保存词汇表"""
        vocab_data = {
            'word2idx': self.word2idx,
            'idx2word': self.idx2word,
            'word_counts': dict(self.word_counts),
            'min_freq': self.min_freq,
            'max_size': self.max_size,
            'special_tokens': {
                'PAD_TOKEN': self.PAD_TOKEN,
                'UNK_TOKEN': self.UNK_TOKEN,
                'START_TOKEN': self.START_TOKEN,
                'END_TOKEN': self.END_TOKEN
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, ensure_ascii=False, indent=2)
        
        print(f"词汇表已保存到: {filepath}")
    
    def load(self, filepath: str):
        """加载词汇表"""
        with open(filepath, 'r', encoding='utf-8') as f:
            vocab_data = json.load(f)
        
        self.word2idx = vocab_data['word2idx']
        self.idx2word = {int(k): v for k, v in vocab_data['idx2word'].items()}
        self.word_counts = Counter(vocab_data['word_counts'])
        self.min_freq = vocab_data['min_freq']
        self.max_size = vocab_data['max_size']
        
        # 加载特殊标记
        special_tokens = vocab_data['special_tokens']
        self.PAD_TOKEN = special_tokens['PAD_TOKEN']
        self.UNK_TOKEN = special_tokens['UNK_TOKEN']
        self.START_TOKEN = special_tokens['START_TOKEN']
        self.END_TOKEN = special_tokens['END_TOKEN']
        
        print(f"词汇表已从 {filepath} 加载")
        print(f"词汇表大小: {len(self.word2idx)}")
    
    def __len__(self):
        return len(self.word2idx)
    
    def __contains__(self, word):
        return word in self.word2idx
    
    def get_word_index(self, word: str) -> int:
        """获取词的索引"""
        return self.word2idx.get(word, self.word2idx[self.UNK_TOKEN])
    
    def get_index_word(self, idx: int) -> str:
        """获取索引对应的词"""
        return self.idx2word.get(idx, self.UNK_TOKEN)
    
    def get_most_common_words(self, n: int = 20) -> List[tuple]:
        """获取最常见的词"""
        return self.word_counts.most_common(n)
    
    def get_vocab_stats(self) -> Dict:
        """获取词汇表统计信息"""
        return {
            'vocab_size': len(self.word2idx),
            'total_words': sum(self.word_counts.values()),
            'unique_words': len(self.word_counts),
            'min_freq': self.min_freq,
            'max_size': self.max_size,
            'avg_word_freq': sum(self.word_counts.values()) / len(self.word_counts) if self.word_counts else 0
        }

def main():
    """测试词汇表"""
    # 示例文本
    texts = [
        "人工智能 技术 发展 趋势",
        "深度 学习 算法 应用",
        "机器 学习 模型 训练",
        "自然 语言 处理 技术",
        "计算机 视觉 图像 识别"
    ]
    
    # 创建词汇表
    vocab = Vocabulary(min_freq=1)
    vocab.build_from_texts(texts)
    
    # 测试编码解码
    test_text = "人工智能 深度 学习"
    encoded = vocab.encode_text(test_text, max_length=10)
    decoded = vocab.decode_indices(encoded)
    
    print(f"原文本: {test_text}")
    print(f"编码: {encoded}")
    print(f"解码: {decoded}")
    
    # 显示统计信息
    stats = vocab.get_vocab_stats()
    print(f"\n词汇表统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 显示最常见的词
    print(f"\n最常见的词:")
    for word, count in vocab.get_most_common_words(10):
        print(f"  {word}: {count}")

if __name__ == "__main__":
    main()
