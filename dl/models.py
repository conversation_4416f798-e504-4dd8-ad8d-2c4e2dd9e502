#!/usr/bin/env python3
"""
深度学习模型定义
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple
import math

try:
    from transformers import BertModel, BertTokenizer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Warning: transformers library not available. BERT model will not work.")

class BertClassifier(nn.Module):
    """基于BERT的文本分类器"""
    
    def __init__(self, model_name: str = 'bert-base-chinese', num_classes: int = 2, 
                 dropout: float = 0.3, freeze_bert: bool = False):
        super(BertClassifier, self).__init__()
        
        self.bert = BertModel.from_pretrained(model_name)
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(self.bert.config.hidden_size, num_classes)
        
        # 是否冻结BERT参数
        if freeze_bert:
            for param in self.bert.parameters():
                param.requires_grad = False
    
    def forward(self, input_ids, attention_mask):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        output = self.dropout(pooled_output)
        return self.classifier(output)

class CNNTextClassifier(nn.Module):
    """CNN文本分类器"""
    
    def __init__(self, vocab_size: int, embed_dim: int = 128, num_filters: int = 100,
                 filter_sizes: list = [3, 4, 5], num_classes: int = 2, dropout: float = 0.5):
        super(CNNTextClassifier, self).__init__()
        
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.convs = nn.ModuleList([
            nn.Conv1d(embed_dim, num_filters, kernel_size=fs)
            for fs in filter_sizes
        ])
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(len(filter_sizes) * num_filters, num_classes)
        
    def forward(self, x):
        # x: (batch_size, seq_len)
        x = self.embedding(x)  # (batch_size, seq_len, embed_dim)
        x = x.transpose(1, 2)  # (batch_size, embed_dim, seq_len)
        
        conv_outputs = []
        for conv in self.convs:
            conv_out = F.relu(conv(x))  # (batch_size, num_filters, conv_seq_len)
            pooled = F.max_pool1d(conv_out, conv_out.size(2))  # (batch_size, num_filters, 1)
            conv_outputs.append(pooled.squeeze(2))  # (batch_size, num_filters)
        
        x = torch.cat(conv_outputs, dim=1)  # (batch_size, len(filter_sizes) * num_filters)
        x = self.dropout(x)
        return self.fc(x)

class LSTMClassifier(nn.Module):
    """LSTM文本分类器"""
    
    def __init__(self, vocab_size: int, embed_dim: int = 128, hidden_dim: int = 128,
                 num_layers: int = 2, num_classes: int = 2, dropout: float = 0.5,
                 bidirectional: bool = True):
        super(LSTMClassifier, self).__init__()
        
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.lstm = nn.LSTM(
            embed_dim, hidden_dim, num_layers,
            batch_first=True, dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional
        )
        
        lstm_output_dim = hidden_dim * 2 if bidirectional else hidden_dim
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(lstm_output_dim, num_classes)
        
    def forward(self, x):
        # x: (batch_size, seq_len)
        x = self.embedding(x)  # (batch_size, seq_len, embed_dim)
        
        lstm_out, (hidden, _) = self.lstm(x)
        
        # 使用最后一个时间步的输出
        if self.lstm.bidirectional:
            # 连接前向和后向的最后隐藏状态
            hidden = torch.cat((hidden[-2], hidden[-1]), dim=1)
        else:
            hidden = hidden[-1]
        
        output = self.dropout(hidden)
        return self.fc(output)

class TransformerClassifier(nn.Module):
    """Transformer编码器分类器"""
    
    def __init__(self, vocab_size: int, embed_dim: int = 128, num_heads: int = 8,
                 num_layers: int = 6, ff_dim: int = 512, max_seq_len: int = 128,
                 num_classes: int = 2, dropout: float = 0.1):
        super(TransformerClassifier, self).__init__()
        
        self.embed_dim = embed_dim
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.pos_encoding = PositionalEncoding(embed_dim, max_seq_len)
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=ff_dim,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(embed_dim, num_classes)
        
    def forward(self, x, mask=None):
        # x: (batch_size, seq_len)
        x = self.embedding(x) * math.sqrt(self.embed_dim)
        x = self.pos_encoding(x)
        
        # 创建padding mask
        if mask is not None:
            # mask: (batch_size, seq_len) -> (batch_size, seq_len)
            # True表示需要被mask的位置
            mask = ~mask.bool()
        
        x = self.transformer(x, src_key_padding_mask=mask)
        
        # 全局平均池化
        if mask is not None:
            # 只对非padding位置求平均
            mask_expanded = mask.unsqueeze(-1).expand_as(x)
            x = x.masked_fill(mask_expanded, 0)
            lengths = (~mask).sum(dim=1, keepdim=True).float()
            x = x.sum(dim=1) / lengths
        else:
            x = x.mean(dim=1)
        
        x = self.dropout(x)
        return self.fc(x)

class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, embed_dim: int, max_seq_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_seq_len, embed_dim)
        position = torch.arange(0, max_seq_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, embed_dim, 2).float() * 
                           (-math.log(10000.0) / embed_dim))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        return x + self.pe[:x.size(1), :].transpose(0, 1)

class EnsembleClassifier(nn.Module):
    """集成分类器"""
    
    def __init__(self, models: list, weights: Optional[list] = None):
        super(EnsembleClassifier, self).__init__()
        
        self.models = nn.ModuleList(models)
        self.weights = weights if weights else [1.0] * len(models)
        
    def forward(self, *args, **kwargs):
        outputs = []
        
        for model in self.models:
            output = model(*args, **kwargs)
            outputs.append(F.softmax(output, dim=1))
        
        # 加权平均
        weighted_output = torch.zeros_like(outputs[0])
        total_weight = sum(self.weights)
        
        for output, weight in zip(outputs, self.weights):
            weighted_output += output * (weight / total_weight)
        
        return torch.log(weighted_output + 1e-8)  # 返回log概率

def get_model(model_type: str, **kwargs) -> nn.Module:
    """模型工厂函数"""
    
    if model_type == 'bert':
        return BertClassifier(**kwargs)
    elif model_type == 'cnn':
        return CNNTextClassifier(**kwargs)
    elif model_type == 'lstm':
        return LSTMClassifier(**kwargs)
    elif model_type == 'transformer':
        return TransformerClassifier(**kwargs)
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

def count_parameters(model: nn.Module) -> int:
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def main():
    """测试模型"""
    
    # 测试BERT模型
    print("测试BERT模型...")
    try:
        bert_model = BertClassifier()
        print(f"BERT模型参数数量: {count_parameters(bert_model):,}")
    except Exception as e:
        print(f"BERT模型测试失败: {e}")
    
    # 测试CNN模型
    print("\n测试CNN模型...")
    cnn_model = CNNTextClassifier(vocab_size=10000)
    print(f"CNN模型参数数量: {count_parameters(cnn_model):,}")
    
    # 测试LSTM模型
    print("\n测试LSTM模型...")
    lstm_model = LSTMClassifier(vocab_size=10000)
    print(f"LSTM模型参数数量: {count_parameters(lstm_model):,}")
    
    # 测试Transformer模型
    print("\n测试Transformer模型...")
    transformer_model = TransformerClassifier(vocab_size=10000)
    print(f"Transformer模型参数数量: {count_parameters(transformer_model):,}")

if __name__ == "__main__":
    main()
