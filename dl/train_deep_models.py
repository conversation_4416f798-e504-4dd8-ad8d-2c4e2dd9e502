#!/usr/bin/env python3
"""
深度学习模型训练主脚本
"""

import os
import sys
import argparse
import torch
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from data_loader import DataLoader as RSSDataLoader, TextPreprocessor
from models import get_model, count_parameters
from trainer import Trainer
from vocabulary import Vocabulary

def set_seed(seed: int = 42):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def train_cnn_model(data_loader: RSSDataLoader, args):
    """训练CNN模型"""
    print("="*60)
    print("训练CNN模型")
    print("="*60)
    
    # 加载和预处理数据
    train_df, test_df = data_loader.load_data()
    train_df = data_loader.preprocess_data(train_df, segment=True)
    test_df = data_loader.preprocess_data(test_df, segment=True)
    
    # 构建词汇表
    vocab = Vocabulary()
    vocab.build_from_texts(train_df['title'].tolist())
    print(f"词汇表大小: {len(vocab)}")
    
    # 创建数据集
    train_dataset, val_dataset, test_dataset = data_loader.create_datasets(
        train_df, test_df, tokenizer=None, max_length=args.max_length, val_size=0.2
    )
    
    # 转换文本为索引
    train_dataset = vocab.encode_dataset(train_dataset)
    val_dataset = vocab.encode_dataset(val_dataset)
    test_dataset = vocab.encode_dataset(test_dataset)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = data_loader.create_data_loaders(
        train_dataset, val_dataset, test_dataset, batch_size=args.batch_size
    )
    
    # 创建模型
    model = get_model('cnn', 
                     vocab_size=len(vocab),
                     embed_dim=args.embed_dim,
                     num_filters=args.num_filters,
                     filter_sizes=[3, 4, 5],
                     num_classes=2,
                     dropout=args.dropout)
    
    print(f"模型参数数量: {count_parameters(model):,}")
    
    # 训练
    trainer = Trainer(model, output_dir=args.output_dir + "/cnn")
    
    train_results = trainer.train(
        train_loader, val_loader,
        num_epochs=args.epochs,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        patience=args.patience
    )
    
    # 评估
    test_results = trainer.evaluate(test_loader)
    
    return {
        'model_type': 'CNN',
        'train_results': train_results,
        'test_results': test_results,
        'vocab_size': len(vocab)
    }

def train_lstm_model(data_loader: RSSDataLoader, args):
    """训练LSTM模型"""
    print("="*60)
    print("训练LSTM模型")
    print("="*60)
    
    # 加载和预处理数据
    train_df, test_df = data_loader.load_data()
    train_df = data_loader.preprocess_data(train_df, segment=True)
    test_df = data_loader.preprocess_data(test_df, segment=True)
    
    # 构建词汇表
    vocab = Vocabulary()
    vocab.build_from_texts(train_df['title'].tolist())
    print(f"词汇表大小: {len(vocab)}")
    
    # 创建数据集
    train_dataset, val_dataset, test_dataset = data_loader.create_datasets(
        train_df, test_df, tokenizer=None, max_length=args.max_length, val_size=0.2
    )
    
    # 转换文本为索引
    train_dataset = vocab.encode_dataset(train_dataset)
    val_dataset = vocab.encode_dataset(val_dataset)
    test_dataset = vocab.encode_dataset(test_dataset)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = data_loader.create_data_loaders(
        train_dataset, val_dataset, test_dataset, batch_size=args.batch_size
    )
    
    # 创建模型
    model = get_model('lstm',
                     vocab_size=len(vocab),
                     embed_dim=args.embed_dim,
                     hidden_dim=args.hidden_dim,
                     num_layers=args.num_layers,
                     num_classes=2,
                     dropout=args.dropout,
                     bidirectional=True)
    
    print(f"模型参数数量: {count_parameters(model):,}")
    
    # 训练
    trainer = Trainer(model, output_dir=args.output_dir + "/lstm")
    
    train_results = trainer.train(
        train_loader, val_loader,
        num_epochs=args.epochs,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        patience=args.patience
    )
    
    # 评估
    test_results = trainer.evaluate(test_loader)
    
    return {
        'model_type': 'LSTM',
        'train_results': train_results,
        'test_results': test_results,
        'vocab_size': len(vocab)
    }

def train_transformer_model(data_loader: RSSDataLoader, args):
    """训练Transformer模型"""
    print("="*60)
    print("训练Transformer模型")
    print("="*60)
    
    # 加载和预处理数据
    train_df, test_df = data_loader.load_data()
    train_df = data_loader.preprocess_data(train_df, segment=True)
    test_df = data_loader.preprocess_data(test_df, segment=True)
    
    # 构建词汇表
    vocab = Vocabulary()
    vocab.build_from_texts(train_df['title'].tolist())
    print(f"词汇表大小: {len(vocab)}")
    
    # 创建数据集
    train_dataset, val_dataset, test_dataset = data_loader.create_datasets(
        train_df, test_df, tokenizer=None, max_length=args.max_length, val_size=0.2
    )
    
    # 转换文本为索引
    train_dataset = vocab.encode_dataset(train_dataset)
    val_dataset = vocab.encode_dataset(val_dataset)
    test_dataset = vocab.encode_dataset(test_dataset)
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = data_loader.create_data_loaders(
        train_dataset, val_dataset, test_dataset, batch_size=args.batch_size
    )
    
    # 创建模型
    model = get_model('transformer',
                     vocab_size=len(vocab),
                     embed_dim=args.embed_dim,
                     num_heads=args.num_heads,
                     num_layers=args.num_layers,
                     ff_dim=args.ff_dim,
                     max_seq_len=args.max_length,
                     num_classes=2,
                     dropout=args.dropout)
    
    print(f"模型参数数量: {count_parameters(model):,}")
    
    # 训练
    trainer = Trainer(model, output_dir=args.output_dir + "/transformer")
    
    train_results = trainer.train(
        train_loader, val_loader,
        num_epochs=args.epochs,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        patience=args.patience
    )
    
    # 评估
    test_results = trainer.evaluate(test_loader)
    
    return {
        'model_type': 'Transformer',
        'train_results': train_results,
        'test_results': test_results,
        'vocab_size': len(vocab)
    }

def train_bert_model(data_loader: RSSDataLoader, args):
    """训练BERT模型"""
    print("="*60)
    print("训练BERT模型")
    print("="*60)
    
    try:
        from transformers import BertTokenizer
        
        # 加载和预处理数据
        train_df, test_df = data_loader.load_data()
        train_df = data_loader.preprocess_data(train_df, segment=False)  # BERT不需要分词
        test_df = data_loader.preprocess_data(test_df, segment=False)
        
        # 创建BERT tokenizer
        tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
        
        # 创建数据集
        train_dataset, val_dataset, test_dataset = data_loader.create_datasets(
            train_df, test_df, tokenizer=tokenizer, max_length=args.max_length, val_size=0.2
        )
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = data_loader.create_data_loaders(
            train_dataset, val_dataset, test_dataset, batch_size=args.batch_size
        )
        
        # 创建模型
        model = get_model('bert',
                         model_name='bert-base-chinese',
                         num_classes=2,
                         dropout=args.dropout,
                         freeze_bert=args.freeze_bert)
        
        print(f"模型参数数量: {count_parameters(model):,}")
        
        # 训练
        trainer = Trainer(model, output_dir=args.output_dir + "/bert")
        
        train_results = trainer.train(
            train_loader, val_loader,
            num_epochs=args.epochs,
            learning_rate=args.bert_learning_rate,  # BERT使用较小的学习率
            weight_decay=args.weight_decay,
            patience=args.patience
        )
        
        # 评估
        test_results = trainer.evaluate(test_loader)
        
        return {
            'model_type': 'BERT',
            'train_results': train_results,
            'test_results': test_results,
            'vocab_size': tokenizer.vocab_size
        }
        
    except ImportError:
        print("transformers库未安装，跳过BERT模型训练")
        return None
    except Exception as e:
        print(f"BERT模型训练失败: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='深度学习RSS文章分类模型训练')
    
    # 数据参数
    parser.add_argument('--data_dir', type=str, default='/Users/<USER>/workplace/rssReco/out',
                       help='数据目录')
    parser.add_argument('--output_dir', type=str, default='/Users/<USER>/workplace/rssReco/out/dl',
                       help='输出目录')
    parser.add_argument('--max_length', type=int, default=128, help='最大序列长度')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=20, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=1e-3, help='学习率')
    parser.add_argument('--bert_learning_rate', type=float, default=2e-5, help='BERT学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--patience', type=int, default=5, help='早停耐心')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    # 模型参数
    parser.add_argument('--embed_dim', type=int, default=128, help='嵌入维度')
    parser.add_argument('--hidden_dim', type=int, default=128, help='隐藏层维度')
    parser.add_argument('--num_filters', type=int, default=100, help='CNN过滤器数量')
    parser.add_argument('--num_layers', type=int, default=2, help='层数')
    parser.add_argument('--num_heads', type=int, default=8, help='注意力头数')
    parser.add_argument('--ff_dim', type=int, default=512, help='前馈网络维度')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout率')
    parser.add_argument('--freeze_bert', action='store_true', help='冻结BERT参数')
    
    # 模型选择
    parser.add_argument('--models', type=str, nargs='+', 
                       choices=['cnn', 'lstm', 'transformer', 'bert', 'all'],
                       default=['all'], help='要训练的模型')
    
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    
    # 创建数据加载器
    data_loader = RSSDataLoader(args.data_dir)
    
    # 训练结果
    all_results = []
    
    # 确定要训练的模型
    if 'all' in args.models:
        models_to_train = ['cnn', 'lstm', 'transformer', 'bert']
    else:
        models_to_train = args.models
    
    print(f"将训练以下模型: {models_to_train}")
    print(f"输出目录: {args.output_dir}")
    print(f"设备: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    print()
    
    # 训练各个模型
    for model_type in models_to_train:
        try:
            if model_type == 'cnn':
                result = train_cnn_model(data_loader, args)
            elif model_type == 'lstm':
                result = train_lstm_model(data_loader, args)
            elif model_type == 'transformer':
                result = train_transformer_model(data_loader, args)
            elif model_type == 'bert':
                result = train_bert_model(data_loader, args)
            
            if result:
                all_results.append(result)
                print(f"\n{result['model_type']} 模型训练完成!")
                print(f"测试准确率: {result['test_results']['accuracy']:.4f}")
                if result['test_results']['roc_auc']:
                    print(f"测试ROC-AUC: {result['test_results']['roc_auc']:.4f}")
            
        except Exception as e:
            print(f"{model_type} 模型训练失败: {e}")
            continue
        
        print("\n" + "="*80 + "\n")
    
    # 总结结果
    if all_results:
        print("="*60)
        print("训练总结")
        print("="*60)
        
        # 按准确率排序
        all_results.sort(key=lambda x: x['test_results']['accuracy'], reverse=True)
        
        print(f"{'模型':<15} {'准确率':<10} {'ROC-AUC':<10} {'参数量':<15}")
        print("-" * 60)
        
        for result in all_results:
            acc = result['test_results']['accuracy']
            auc = result['test_results']['roc_auc'] or 0.0
            vocab_size = result.get('vocab_size', 'N/A')
            
            print(f"{result['model_type']:<15} {acc:<10.4f} {auc:<10.4f} {vocab_size}")
        
        # 与逻辑回归对比
        print(f"\n逻辑回归基线:")
        print(f"{'LR (baseline)':<15} {'0.6936':<10} {'0.7347':<10} {'166532'}")
        
        best_model = all_results[0]
        improvement = best_model['test_results']['accuracy'] - 0.6936
        print(f"\n最佳模型 ({best_model['model_type']}) 相比逻辑回归提升: {improvement:+.4f}")
        
    else:
        print("没有成功训练任何模型")

if __name__ == "__main__":
    main()
